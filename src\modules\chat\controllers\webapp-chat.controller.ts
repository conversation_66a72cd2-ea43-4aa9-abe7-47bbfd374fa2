import {
  <PERSON>,
  Get,
  Post,
  Body,
  Query,
  Param,
  ParseInt<PERSON>ipe,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { WebappChatService } from '../services/webapp-chat.service';
import {
  WebappChatMessageDto,
  ChatResponseDto,
  CreateConversationDto,
  ConversationHistoryDto,
  ConversationHistoryResponseDto,
} from '../dto/webapp-chat.dto';
import { ApiResponseDto } from '@/common/response/api-response-dto';

/**
 * Controller xử lý REST API cho webapp chat
 */
@ApiTags('Webapp Chat')
@Controller('webapp-chat')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class WebappChatController {
  private readonly logger = new Logger(WebappChatController.name);

  constructor(private readonly webappChatService: WebappChatService) {}

  /**
   * Tạo cuộc hội thoại mới
   */
  @Post('conversations')
  @ApiOperation({
    summary: 'Tạo cuộc hội thoại mới',
    description: 'Tạo một cuộc hội thoại chat mới cho người dùng webapp',
  })
  @ApiResponse({
    status: 201,
    description: 'Cuộc hội thoại đã được tạo thành công',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
  })
  async createConversation(
    @CurrentUser() user: JwtPayload,
    @Body() createConversationDto: CreateConversationDto,
  ): Promise<ApiResponseDto> {
    try {
      const tenantId = Number(user.tenantId);
      
      this.logger.log(
        `Creating new conversation for user ${user.id}, tenant ${tenantId}`,
      );

      const conversation = await this.webappChatService.createConversation(
        user.id,
        tenantId,
        createConversationDto,
      );

      return {
        success: true,
        message: 'Cuộc hội thoại đã được tạo thành công',
        data: {
          conversationId: conversation.id,
          status: conversation.status,
          language: conversation.language,
          createdAt: conversation.createdAt,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to create conversation for user ${user.id}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Lấy hoặc tạo cuộc hội thoại active
   */
  @Get('conversations/active')
  @ApiOperation({
    summary: 'Lấy cuộc hội thoại active',
    description: 'Lấy cuộc hội thoại đang hoạt động hoặc tạo mới nếu chưa có',
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin cuộc hội thoại active',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
  })
  async getActiveConversation(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto> {
    try {
      const tenantId = Number(user.tenantId);
      
      this.logger.log(
        `Getting active conversation for user ${user.id}, tenant ${tenantId}`,
      );

      const conversation = await this.webappChatService.getOrCreateActiveConversation(
        user.id,
        tenantId,
      );

      return {
        success: true,
        message: 'Lấy cuộc hội thoại thành công',
        data: {
          conversationId: conversation.id,
          status: conversation.status,
          language: conversation.language,
          createdAt: conversation.createdAt,
          lastMessageAt: conversation.lastMessageAt,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get active conversation for user ${user.id}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Gửi tin nhắn và nhận phản hồi AI
   */
  @Post('messages')
  @ApiOperation({
    summary: 'Gửi tin nhắn',
    description: 'Gửi tin nhắn và nhận phản hồi từ AI agent',
  })
  @ApiResponse({
    status: 201,
    description: 'Tin nhắn đã được xử lý và có phản hồi từ AI',
    type: ChatResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu tin nhắn không hợp lệ',
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
  })
  @ApiResponse({
    status: 404,
    description: 'Cuộc hội thoại không tồn tại',
  })
  async sendMessage(
    @CurrentUser() user: JwtPayload,
    @Body() messageDto: WebappChatMessageDto,
  ): Promise<ChatResponseDto> {
    try {
      const tenantId = Number(user.tenantId);
      
      this.logger.log(
        `Processing message from user ${user.id}: ${messageDto.content}`,
      );

      const response = await this.webappChatService.processWebappMessage(
        user.id,
        tenantId,
        messageDto,
      );

      this.logger.log(
        `Sent AI response to user ${user.id}, conversation ${response.conversationId}`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Failed to process message for user ${user.id}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Lấy lịch sử cuộc hội thoại
   */
  @Get('conversations/:conversationId/history')
  @ApiOperation({
    summary: 'Lấy lịch sử cuộc hội thoại',
    description: 'Lấy danh sách tin nhắn trong cuộc hội thoại với phân trang',
  })
  @ApiParam({
    name: 'conversationId',
    description: 'ID cuộc hội thoại',
    type: 'number',
  })
  @ApiQuery({
    name: 'page',
    description: 'Số trang (mặc định: 1)',
    required: false,
    type: 'number',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Số tin nhắn mỗi trang (mặc định: 50)',
    required: false,
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Lịch sử cuộc hội thoại',
    type: ConversationHistoryResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
  })
  @ApiResponse({
    status: 404,
    description: 'Cuộc hội thoại không tồn tại',
  })
  async getConversationHistory(
    @CurrentUser() user: JwtPayload,
    @Param('conversationId', ParseIntPipe) conversationId: number,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  ): Promise<ConversationHistoryResponseDto> {
    try {
      const tenantId = Number(user.tenantId);
      
      this.logger.log(
        `Getting conversation history for user ${user.id}, conversation ${conversationId}`,
      );

      const query: ConversationHistoryDto = {
        conversationId,
        page: page || 1,
        limit: limit || 50,
      };

      const history = await this.webappChatService.getConversationHistory(
        user.id,
        tenantId,
        query,
      );

      return history;
    } catch (error) {
      this.logger.error(
        `Failed to get conversation history for user ${user.id}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Đóng cuộc hội thoại
   */
  @Post('conversations/:conversationId/close')
  @ApiOperation({
    summary: 'Đóng cuộc hội thoại',
    description: 'Đóng cuộc hội thoại hiện tại',
  })
  @ApiParam({
    name: 'conversationId',
    description: 'ID cuộc hội thoại',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Cuộc hội thoại đã được đóng',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
  })
  @ApiResponse({
    status: 404,
    description: 'Cuộc hội thoại không tồn tại',
  })
  async closeConversation(
    @CurrentUser() user: JwtPayload,
    @Param('conversationId', ParseIntPipe) conversationId: number,
  ): Promise<ApiResponseDto> {
    try {
      const tenantId = Number(user.tenantId);
      
      this.logger.log(
        `Closing conversation ${conversationId} for user ${user.id}`,
      );

      await this.webappChatService.closeConversation(
        user.id,
        tenantId,
        conversationId,
      );

      return {
        success: true,
        message: 'Cuộc hội thoại đã được đóng thành công',
        data: {
          conversationId,
          status: 'closed',
          closedAt: Date.now(),
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to close conversation ${conversationId} for user ${user.id}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra trạng thái kết nối AI agent
   */
  @Get('agent/status')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái AI agent',
    description: 'Kiểm tra trạng thái hoạt động của AI agent',
  })
  @ApiResponse({
    status: 200,
    description: 'Trạng thái AI agent',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
  })
  async getAgentStatus(@CurrentUser() user: JwtPayload): Promise<ApiResponseDto> {
    try {
      this.logger.log(`Checking agent status for user ${user.id}`);

      // Kiểm tra trạng thái AI agent (có thể mở rộng thêm logic kiểm tra)
      const agentStatus = {
        status: 'online',
        version: '1.0.0',
        capabilities: [
          'text_processing',
          'intent_recognition',
          'business_data_query',
          'multilingual_support',
        ],
        lastHealthCheck: Date.now(),
      };

      return {
        success: true,
        message: 'Trạng thái AI agent',
        data: agentStatus,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get agent status for user ${user.id}: ${error.message}`,
      );
      throw error;
    }
  }
}
