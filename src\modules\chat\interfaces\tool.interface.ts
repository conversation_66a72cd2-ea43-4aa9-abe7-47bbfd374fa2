/**
 * Interface định nghĩa cấu trúc của một tool/function
 */
export interface ToolDefinition {
  /** Tên unique của tool */
  name: string;
  
  /** <PERSON><PERSON> tả chức năng của tool */
  description: string;
  
  /** Schema định nghĩa parameters theo JSON Schema format */
  parameters: {
    type: 'object';
    properties: Record<string, ToolParameter>;
    required: string[];
  };
  
  /** Function handler thực thi tool */
  handler: (params: any, context: ToolContext) => Promise<ToolResult>;
  
  /** Phân loại tool */
  category: ToolCategory;
  
  /** Tool có cần tenant isolation không */
  tenantIsolated: boolean;
  
  /** Quyền cần thiết để sử dụng tool */
  requiredPermissions?: string[];
  
  /** Rate limit cho tool (calls per minute) */
  rateLimit?: number;
  
  /** Timeout cho tool execution (milliseconds) */
  timeout?: number;
  
  /** Tool có cache được kết quả không */
  cacheable?: boolean;
  
  /** Thời gian cache (seconds) */
  cacheTtl?: number;
}

/**
 * Định nghĩa parameter của tool
 */
export interface ToolParameter {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  enum?: any[];
  minimum?: number;
  maximum?: number;
  pattern?: string;
  items?: ToolParameter;
  properties?: Record<string, ToolParameter>;
  default?: any;
}

/**
 * Context được truyền vào tool khi thực thi
 */
export interface ToolContext {
  /** ID tenant hiện tại */
  tenantId: number;
  
  /** ID người dùng hiện tại */
  userId: number;
  
  /** ID cuộc hội thoại */
  conversationId: number;
  
  /** Thông tin người dùng */
  user?: {
    id: number;
    username: string;
    email: string;
    roles: string[];
  };
  
  /** Context bổ sung từ cuộc hội thoại */
  userContext?: any;
  
  /** Metadata bổ sung */
  metadata?: Record<string, any>;
  
  /** Thời gian thực thi */
  timestamp: number;
}

/**
 * Kết quả trả về từ tool execution
 */
export interface ToolResult {
  /** Kết quả thành công hay không */
  success: boolean;
  
  /** Dữ liệu trả về */
  data?: any;
  
  /** Thông báo lỗi nếu có */
  error?: string;
  
  /** Thông báo cho người dùng */
  message?: string;
  
  /** Metadata bổ sung */
  metadata?: {
    executionTime?: number;
    fromCache?: boolean;
    rowCount?: number;
    [key: string]: any;
  };
  
  /** Gợi ý actions tiếp theo */
  suggestions?: Array<{
    title: string;
    action: string;
    parameters?: any;
  }>;
}

/**
 * Phân loại tool
 */
export enum ToolCategory {
  /** Tools liên quan đến database */
  DATABASE = 'database',
  
  /** Tools liên quan đến business logic */
  BUSINESS = 'business',
  
  /** Tools tiện ích */
  UTILITY = 'utility',
  
  /** Tools gọi external services */
  EXTERNAL = 'external',
  
  /** Tools liên quan đến file/document */
  FILE = 'file',
  
  /** Tools liên quan đến notification */
  NOTIFICATION = 'notification',
  
  /** Tools liên quan đến analytics */
  ANALYTICS = 'analytics'
}

/**
 * Kết quả thực thi tool với thông tin bổ sung
 */
export interface ToolExecutionResult extends ToolResult {
  /** Tên tool đã thực thi */
  toolName: string;
  
  /** Parameters đã sử dụng */
  parameters: any;
  
  /** Thời gian bắt đầu thực thi */
  startTime: number;
  
  /** Thời gian kết thúc thực thi */
  endTime: number;
  
  /** Thời gian thực thi (ms) */
  executionTime: number;
  
  /** Tool context */
  context: ToolContext;
}

/**
 * Thống kê performance của tool
 */
export interface ToolStats {
  /** Tên tool */
  toolName: string;
  
  /** Tổng số lần gọi */
  totalCalls: number;
  
  /** Số lần thành công */
  successCalls: number;
  
  /** Số lần thất bại */
  failedCalls: number;
  
  /** Tỷ lệ thành công */
  successRate: number;
  
  /** Thời gian thực thi trung bình (ms) */
  averageExecutionTime: number;
  
  /** Thời gian thực thi tối đa (ms) */
  maxExecutionTime: number;
  
  /** Thời gian thực thi tối thiểu (ms) */
  minExecutionTime: number;
  
  /** Số lần hit cache */
  cacheHits: number;
  
  /** Tỷ lệ hit cache */
  cacheHitRate: number;
  
  /** Thời gian cập nhật cuối */
  lastUpdated: number;
}

/**
 * Cấu hình tool registry
 */
export interface ToolRegistryConfig {
  /** Có enable caching không */
  enableCaching: boolean;
  
  /** Default cache TTL (seconds) */
  defaultCacheTtl: number;
  
  /** Default timeout (milliseconds) */
  defaultTimeout: number;
  
  /** Default rate limit (calls per minute) */
  defaultRateLimit: number;
  
  /** Có enable monitoring không */
  enableMonitoring: boolean;
  
  /** Có enable audit logging không */
  enableAuditLogging: boolean;
}

/**
 * Tool validation error
 */
export interface ToolValidationError {
  /** Field bị lỗi */
  field: string;
  
  /** Thông báo lỗi */
  message: string;
  
  /** Giá trị hiện tại */
  value: any;
  
  /** Giá trị mong đợi */
  expected?: any;
}

/**
 * Tool execution options
 */
export interface ToolExecutionOptions {
  /** Có sử dụng cache không */
  useCache?: boolean;
  
  /** Custom timeout */
  timeout?: number;
  
  /** Có retry khi thất bại không */
  retry?: boolean;
  
  /** Số lần retry tối đa */
  maxRetries?: number;
  
  /** Có log execution không */
  logExecution?: boolean;
  
  /** Priority của execution */
  priority?: 'low' | 'normal' | 'high';
}

/**
 * Tool anomaly detection
 */
export interface ToolAnomaly {
  /** Tên tool */
  toolName: string;
  
  /** Loại anomaly */
  type: 'high_failure_rate' | 'slow_execution' | 'unusual_usage' | 'error_spike';
  
  /** Mô tả anomaly */
  description: string;
  
  /** Severity level */
  severity: 'low' | 'medium' | 'high' | 'critical';
  
  /** Thời gian phát hiện */
  detectedAt: number;
  
  /** Dữ liệu liên quan */
  data: any;
  
  /** Gợi ý khắc phục */
  recommendations?: string[];
}
