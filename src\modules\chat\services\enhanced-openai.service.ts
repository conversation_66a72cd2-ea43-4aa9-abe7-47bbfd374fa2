import { Injectable, Logger } from '@nestjs/common';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { ToolDefinition } from '../interfaces/tool.interface';

/**
 * Interface cho OpenAI Function Calling response
 */
export interface FunctionCallResponse {
  /** Tin nhắn text từ AI */
  message: string;
  
  /** Danh sách function calls được yêu cầu */
  functionCalls: Array<{
    name: string;
    parameters: any;
    callId: string;
  }>;
  
  /** Có cần gọi function không */
  requiresFunctionCall: boolean;
  
  /** Finish reason */
  finishReason: 'stop' | 'function_call' | 'tool_calls' | 'length';
  
  /** Usage information */
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

/**
 * Interface cho function call result
 */
export interface FunctionCallResult {
  callId: string;
  name: string;
  result: any;
  success: boolean;
  error?: string;
}

/**
 * Enhanced OpenAI Service với Function Calling support
 */
@Injectable()
export class EnhancedOpenAiService extends OpenAiService {
  private readonly logger = new Logger(EnhancedOpenAiService.name);

  /**
   * Chat completion với function calling support
   */
  async chatCompletionWithFunctions(
    messages: any[],
    tools: ToolDefinition[],
    model: string = 'gpt-4',
    options: {
      temperature?: number;
      maxTokens?: number;
      toolChoice?: 'auto' | 'none' | { type: 'function'; function: { name: string } };
    } = {}
  ): Promise<FunctionCallResponse> {
    try {
      // Convert tools to OpenAI function format
      const functions = this.convertToolsToFunctions(tools);

      // Prepare request
      const requestBody: any = {
        model,
        messages,
        temperature: options.temperature ?? 0.7,
        max_tokens: options.maxTokens ?? 1000,
      };

      // Add functions if available
      if (functions.length > 0) {
        requestBody.tools = functions;
        requestBody.tool_choice = options.toolChoice ?? 'auto';
      }

      this.logger.log(`Making function call request with ${functions.length} tools available`);

      // Make API call
      const response = await this.openai.chat.completions.create(requestBody);

      const choice = response.choices[0];
      const message = choice.message;

      // Parse response
      const result: FunctionCallResponse = {
        message: message.content || '',
        functionCalls: [],
        requiresFunctionCall: false,
        finishReason: choice.finish_reason as any,
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens,
        } : undefined,
      };

      // Check for tool calls
      if (message.tool_calls && message.tool_calls.length > 0) {
        result.requiresFunctionCall = true;
        result.functionCalls = message.tool_calls.map((toolCall: any) => ({
          name: toolCall.function.name,
          parameters: JSON.parse(toolCall.function.arguments || '{}'),
          callId: toolCall.id,
        }));

        this.logger.log(`AI requested ${result.functionCalls.length} function calls`);
      }

      return result;

    } catch (error) {
      this.logger.error(`Function calling error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Continue conversation sau khi function calls được thực thi
   */
  async continueConversationWithFunctionResults(
    messages: any[],
    functionResults: FunctionCallResult[],
    tools: ToolDefinition[],
    model: string = 'gpt-4'
  ): Promise<FunctionCallResponse> {
    try {
      // Add function results to messages
      const updatedMessages = [...messages];

      // Add function call results as tool messages
      for (const result of functionResults) {
        updatedMessages.push({
          role: 'tool',
          tool_call_id: result.callId,
          content: result.success 
            ? JSON.stringify(result.result)
            : `Error: ${result.error}`,
        });
      }

      this.logger.log(`Continuing conversation with ${functionResults.length} function results`);

      // Make another API call with function results
      return this.chatCompletionWithFunctions(updatedMessages, tools, model);

    } catch (error) {
      this.logger.error(`Error continuing conversation with function results: ${error.message}`);
      throw error;
    }
  }

  /**
   * Simplified function calling - thực hiện toàn bộ flow
   */
  async processWithFunctions(
    userMessage: string,
    tools: ToolDefinition[],
    systemPrompt?: string,
    conversationHistory: any[] = [],
    model: string = 'gpt-4',
    maxIterations: number = 3
  ): Promise<{
    finalMessage: string;
    functionCalls: Array<{
      name: string;
      parameters: any;
      result: any;
    }>;
    totalTokens: number;
  }> {
    try {
      let messages = [...conversationHistory];
      
      // Add system prompt if provided
      if (systemPrompt) {
        messages.unshift({
          role: 'system',
          content: systemPrompt,
        });
      }

      // Add user message
      messages.push({
        role: 'user',
        content: userMessage,
      });

      let totalTokens = 0;
      const allFunctionCalls: Array<{ name: string; parameters: any; result: any }> = [];
      let iterations = 0;

      while (iterations < maxIterations) {
        iterations++;

        // Get AI response
        const response = await this.chatCompletionWithFunctions(messages, tools, model);
        totalTokens += response.usage?.totalTokens || 0;

        // Add AI message to conversation
        if (response.message) {
          messages.push({
            role: 'assistant',
            content: response.message,
          });
        }

        // If no function calls needed, return final response
        if (!response.requiresFunctionCall || response.functionCalls.length === 0) {
          return {
            finalMessage: response.message,
            functionCalls: allFunctionCalls,
            totalTokens,
          };
        }

        // Prepare function results (placeholder - actual execution will be done by caller)
        const functionResults: FunctionCallResult[] = response.functionCalls.map(call => ({
          callId: call.callId,
          name: call.name,
          result: { 
            _placeholder: true, 
            name: call.name, 
            parameters: call.parameters 
          },
          success: true,
        }));

        // Record function calls
        allFunctionCalls.push(...response.functionCalls.map(call => ({
          name: call.name,
          parameters: call.parameters,
          result: null, // Will be filled by actual execution
        })));

        // Continue conversation with function results
        const continueResponse = await this.continueConversationWithFunctionResults(
          messages,
          functionResults,
          tools,
          model
        );

        totalTokens += continueResponse.usage?.totalTokens || 0;

        // If no more function calls, return final response
        if (!continueResponse.requiresFunctionCall) {
          return {
            finalMessage: continueResponse.message,
            functionCalls: allFunctionCalls,
            totalTokens,
          };
        }

        // Update messages for next iteration
        messages = [...messages];
      }

      // Max iterations reached
      this.logger.warn(`Max iterations (${maxIterations}) reached for function calling`);
      return {
        finalMessage: 'Tôi đã thử xử lý yêu cầu của bạn nhưng cần quá nhiều bước. Vui lòng thử lại với yêu cầu đơn giản hơn.',
        functionCalls: allFunctionCalls,
        totalTokens,
      };

    } catch (error) {
      this.logger.error(`Error in processWithFunctions: ${error.message}`);
      throw error;
    }
  }

  /**
   * Convert ToolDefinition to OpenAI function format
   */
  private convertToolsToFunctions(tools: ToolDefinition[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      },
    }));
  }

  /**
   * Analyze message để xác định có cần function calling không
   */
  async analyzeForFunctionCalling(
    message: string,
    availableTools: ToolDefinition[]
  ): Promise<{
    needsFunctionCall: boolean;
    suggestedTools: string[];
    confidence: number;
  }> {
    try {
      const toolNames = availableTools.map(t => t.name).join(', ');
      
      const analysisPrompt = `
Phân tích tin nhắn sau và xác định có cần gọi function/tool không:

Tin nhắn: "${message}"

Các tools có sẵn: ${toolNames}

Trả về JSON với format:
{
  "needsFunctionCall": boolean,
  "suggestedTools": ["tool1", "tool2"],
  "confidence": number (0-1),
  "reasoning": "string"
}
`;

      const response = await this.simpleChatCompletion(
        analysisPrompt,
        'Bạn là chuyên gia phân tích để xác định khi nào cần gọi function/tool.',
        'gpt-3.5-turbo'
      );

      const analysis = JSON.parse(response);
      return {
        needsFunctionCall: analysis.needsFunctionCall || false,
        suggestedTools: analysis.suggestedTools || [],
        confidence: analysis.confidence || 0.5,
      };

    } catch (error) {
      this.logger.error(`Error analyzing for function calling: ${error.message}`);
      return {
        needsFunctionCall: false,
        suggestedTools: [],
        confidence: 0,
      };
    }
  }

  /**
   * Generate system prompt cho function calling
   */
  generateFunctionCallingSystemPrompt(availableTools: ToolDefinition[]): string {
    const toolDescriptions = availableTools.map(tool => 
      `- ${tool.name}: ${tool.description}`
    ).join('\n');

    return `Bạn là trợ lý AI thông minh có khả năng sử dụng các tools/functions để trả lời câu hỏi.

Các tools có sẵn:
${toolDescriptions}

Hướng dẫn:
1. Phân tích câu hỏi của người dùng
2. Xác định tools nào cần thiết để trả lời
3. Gọi tools với parameters phù hợp
4. Sử dụng kết quả từ tools để tạo câu trả lời hoàn chỉnh
5. Trả lời bằng tiếng Việt, rõ ràng và hữu ích

Lưu ý:
- Chỉ gọi tools khi thực sự cần thiết
- Kiểm tra kỹ parameters trước khi gọi
- Xử lý lỗi một cách graceful
- Đưa ra gợi ý nếu không thể thực hiện yêu cầu`;
  }
}
