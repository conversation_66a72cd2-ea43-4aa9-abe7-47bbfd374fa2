import { Injectable, Logger } from '@nestjs/common';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { ToolDefinition } from '../interfaces/tool.interface';

/**
 * Interface cho OpenAI Function Calling response
 */
export interface FunctionCallResponse {
  /** Tin nhắn text từ AI */
  message: string;
  
  /** Danh sách function calls được yêu cầu */
  functionCalls: Array<{
    name: string;
    parameters: any;
    callId: string;
  }>;
  
  /** Có cần gọi function không */
  requiresFunctionCall: boolean;
  
  /** Finish reason */
  finishReason: 'stop' | 'function_call' | 'tool_calls' | 'length';
  
  /** Usage information */
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

/**
 * Interface cho function call result
 */
export interface FunctionCallResult {
  callId: string;
  name: string;
  result: any;
  success: boolean;
  error?: string;
}

/**
 * Enhanced OpenAI Service với Function Calling support
 */
@Injectable()
export class EnhancedOpenAiService {
  private readonly logger = new Logger(EnhancedOpenAiService.name);

  constructor(private readonly openAiService: OpenAiService) {}

  /**
   * Chat completion với function calling support (Simplified version)
   */
  async chatCompletionWithFunctions(
    messages: any[],
    tools: ToolDefinition[],
    model: string = 'gpt-4',
    options: {
      temperature?: number;
      maxTokens?: number;
      toolChoice?: 'auto' | 'none' | { type: 'function'; function: { name: string } };
    } = {}
  ): Promise<FunctionCallResponse> {
    try {
      this.logger.log(`Processing message with ${tools.length} tools available`);

      // Simplified approach: Analyze message to determine if tools are needed
      const lastMessage = messages[messages.length - 1];
      const userMessage = lastMessage?.content || '';

      // Use simple analysis to determine tool needs
      const analysis = await this.analyzeForFunctionCalling(userMessage, tools);

      if (!analysis.needsFunctionCall || analysis.confidence < 0.7) {
        // No function calling needed, return regular response
        const response = await this.openAiService.simpleChatCompletion(
          userMessage,
          'Bạn là trợ lý AI hữu ích của hệ thống ERP. Trả lời câu hỏi một cách ngắn gọn và chính xác.',
          model
        );

        return {
          message: response,
          functionCalls: [],
          requiresFunctionCall: false,
          finishReason: 'stop',
        };
      }

      // Function calling needed - create mock function calls based on analysis
      const functionCalls = analysis.suggestedTools.map((toolName, index) => ({
        name: toolName,
        parameters: this.extractParametersForTool(userMessage, toolName, tools),
        callId: `call_${Date.now()}_${index}`,
      }));

      this.logger.log(`AI requested ${functionCalls.length} function calls: ${functionCalls.map(f => f.name).join(', ')}`);

      return {
        message: '', // Will be filled after function execution
        functionCalls,
        requiresFunctionCall: true,
        finishReason: 'tool_calls',
      };

    } catch (error) {
      this.logger.error(`Function calling error: ${error.message}`);
      throw error;
    }
  }



  /**
   * Simplified function calling - thực hiện toàn bộ flow
   */
  async processWithFunctions(
    userMessage: string,
    tools: ToolDefinition[],
    systemPrompt?: string,
    conversationHistory: any[] = [],
    model: string = 'gpt-4',
    maxIterations: number = 3
  ): Promise<{
    finalMessage: string;
    functionCalls: Array<{
      name: string;
      parameters: any;
      result: any;
    }>;
    totalTokens: number;
  }> {
    try {
      this.logger.log(`Processing message with ${tools.length} available tools`);

      // Analyze if function calling is needed
      const analysis = await this.analyzeForFunctionCalling(userMessage, tools);

      if (!analysis.needsFunctionCall || analysis.confidence < 0.7) {
        // No function calling needed
        const response = await this.openAiService.simpleChatCompletion(
          userMessage,
          systemPrompt || 'Bạn là trợ lý AI hữu ích của hệ thống ERP.',
          model
        );

        return {
          finalMessage: response,
          functionCalls: [],
          totalTokens: 0, // Simplified - không track tokens
        };
      }

      // Function calling needed
      const functionCalls = analysis.suggestedTools.map(toolName => ({
        name: toolName,
        parameters: this.extractParametersForTool(userMessage, toolName, tools),
        result: null, // Will be filled by actual execution
      }));

      this.logger.log(`Suggested function calls: ${functionCalls.map(f => f.name).join(', ')}`);

      return {
        finalMessage: '', // Will be filled after function execution
        functionCalls,
        totalTokens: 0, // Simplified
      };

    } catch (error) {
      this.logger.error(`Error in processWithFunctions: ${error.message}`);
      throw error;
    }
  }

  /**
   * Extract parameters for a specific tool from user message
   */
  private extractParametersForTool(
    userMessage: string,
    toolName: string,
    tools: ToolDefinition[]
  ): any {
    const tool = tools.find(t => t.name === toolName);
    if (!tool) return {};

    // Simple parameter extraction based on tool type
    const params: any = {};

    switch (toolName) {
      case 'get_todo_statistics':
        if (userMessage.includes('hôm nay') || userMessage.includes('today')) {
          params.dateRange = 'today';
        } else if (userMessage.includes('tuần') || userMessage.includes('week')) {
          params.dateRange = 'week';
        } else if (userMessage.includes('tháng') || userMessage.includes('month')) {
          params.dateRange = 'month';
        }
        break;

      case 'get_employee_info':
        // Extract employee name if mentioned
        const nameMatch = userMessage.match(/nhân viên\s+([^\s]+(?:\s+[^\s]+)*)/i);
        if (nameMatch) {
          params.employeeName = nameMatch[1];
        }
        break;

      case 'get_late_employees':
        if (userMessage.includes('hôm nay') || userMessage.includes('today')) {
          params.date = new Date().toISOString().split('T')[0];
        }
        break;

      case 'get_overdue_tasks':
        if (userMessage.includes('urgent') || userMessage.includes('khẩn cấp')) {
          params.priority = 'urgent';
        } else if (userMessage.includes('high') || userMessage.includes('cao')) {
          params.priority = 'high';
        }
        break;

      case 'get_user_tasks':
        if (userMessage.includes('pending') || userMessage.includes('chờ')) {
          params.status = 'pending';
        } else if (userMessage.includes('completed') || userMessage.includes('hoàn thành')) {
          params.status = 'completed';
        }
        break;

      default:
        // For other tools, return empty params
        break;
    }

    return params;
  }

  /**
   * Convert ToolDefinition to OpenAI function format
   */
  private convertToolsToFunctions(tools: ToolDefinition[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      },
    }));
  }

  /**
   * Analyze message để xác định có cần function calling không
   */
  async analyzeForFunctionCalling(
    message: string,
    availableTools: ToolDefinition[]
  ): Promise<{
    needsFunctionCall: boolean;
    suggestedTools: string[];
    confidence: number;
  }> {
    try {
      const toolNames = availableTools.map(t => t.name).join(', ');
      
      const analysisPrompt = `
Phân tích tin nhắn sau và xác định có cần gọi function/tool không:

Tin nhắn: "${message}"

Các tools có sẵn: ${toolNames}

Trả về JSON với format:
{
  "needsFunctionCall": boolean,
  "suggestedTools": ["tool1", "tool2"],
  "confidence": number (0-1),
  "reasoning": "string"
}
`;

      const response = await this.openAiService.simpleChatCompletion(
        analysisPrompt,
        'Bạn là chuyên gia phân tích để xác định khi nào cần gọi function/tool.',
        'gpt-3.5-turbo'
      );

      const analysis = JSON.parse(response);
      return {
        needsFunctionCall: analysis.needsFunctionCall || false,
        suggestedTools: analysis.suggestedTools || [],
        confidence: analysis.confidence || 0.5,
      };

    } catch (error) {
      this.logger.error(`Error analyzing for function calling: ${error.message}`);
      return {
        needsFunctionCall: false,
        suggestedTools: [],
        confidence: 0,
      };
    }
  }

  /**
   * Generate system prompt cho function calling
   */
  generateFunctionCallingSystemPrompt(availableTools: ToolDefinition[]): string {
    const toolDescriptions = availableTools.map(tool => 
      `- ${tool.name}: ${tool.description}`
    ).join('\n');

    return `Bạn là trợ lý AI thông minh có khả năng sử dụng các tools/functions để trả lời câu hỏi.

Các tools có sẵn:
${toolDescriptions}

Hướng dẫn:
1. Phân tích câu hỏi của người dùng
2. Xác định tools nào cần thiết để trả lời
3. Gọi tools với parameters phù hợp
4. Sử dụng kết quả từ tools để tạo câu trả lời hoàn chỉnh
5. Trả lời bằng tiếng Việt, rõ ràng và hữu ích

Lưu ý:
- Chỉ gọi tools khi thực sự cần thiết
- Kiểm tra kỹ parameters trước khi gọi
- Xử lý lỗi một cách graceful
- Đưa ra gợi ý nếu không thể thực hiện yêu cầu`;
  }
}
