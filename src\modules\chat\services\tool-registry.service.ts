import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { RedisService } from '@shared/services/redis.service';
import {
  ToolDefinition,
  ToolContext,
  ToolResult,
  ToolExecutionResult,
  ToolStats,
  ToolCategory,
  ToolValidationError,
  ToolExecutionOptions,
  ToolRegistryConfig,
} from '../interfaces/tool.interface';

/**
 * Service quản lý registry và execution của tools
 */
@Injectable()
export class ToolRegistryService implements OnModuleInit {
  private readonly logger = new Logger(ToolRegistryService.name);
  private readonly tools: Map<string, ToolDefinition> = new Map();
  private readonly executionStats: Map<string, ToolStats> = new Map();
  private readonly rateLimitCounters: Map<string, Map<string, number>> = new Map();

  private readonly config: ToolRegistryConfig = {
    enableCaching: true,
    defaultCacheTtl: 300, // 5 minutes
    defaultTimeout: 30000, // 30 seconds
    defaultRateLimit: 60, // 60 calls per minute
    enableMonitoring: true,
    enableAuditLogging: true,
  };

  constructor(private readonly redisService: RedisService) {}

  async onModuleInit() {
    this.logger.log('Initializing Tool Registry Service');
    await this.loadToolStats();
    this.startRateLimitCleanup();
  }

  /**
   * Đăng ký một tool mới
   */
  registerTool(tool: ToolDefinition): void {
    try {
      // Validate tool definition
      this.validateToolDefinition(tool);

      // Set default values
      const toolWithDefaults: ToolDefinition = {
        ...tool,
        timeout: tool.timeout || this.config.defaultTimeout,
        rateLimit: tool.rateLimit || this.config.defaultRateLimit,
        cacheable: tool.cacheable ?? true,
        cacheTtl: tool.cacheTtl || this.config.defaultCacheTtl,
      };

      this.tools.set(tool.name, toolWithDefaults);
      
      // Initialize stats
      this.initializeToolStats(tool.name);

      this.logger.log(`Registered tool: ${tool.name} (${tool.category})`);
    } catch (error) {
      this.logger.error(`Failed to register tool ${tool.name}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách tools có sẵn
   */
  getAvailableTools(category?: ToolCategory): ToolDefinition[] {
    const allTools = Array.from(this.tools.values());
    
    if (category) {
      return allTools.filter(tool => tool.category === category);
    }
    
    return allTools;
  }

  /**
   * Lấy tool definition theo tên
   */
  getTool(name: string): ToolDefinition | null {
    return this.tools.get(name) || null;
  }

  /**
   * Thực thi một tool
   */
  async executeTool(
    name: string,
    parameters: any,
    context: ToolContext,
    options: ToolExecutionOptions = {}
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Lấy tool definition
      const tool = this.getTool(name);
      if (!tool) {
        throw new Error(`Tool '${name}' not found`);
      }

      // Check rate limit
      await this.checkRateLimit(name, context.userId);

      // Validate parameters
      const validationErrors = this.validateToolParameters(tool, parameters);
      if (validationErrors.length > 0) {
        throw new Error(`Parameter validation failed: ${validationErrors.map(e => e.message).join(', ')}`);
      }

      // Check cache if enabled
      if (tool.cacheable && options.useCache !== false && this.config.enableCaching) {
        const cachedResult = await this.getCachedResult(name, parameters, context);
        if (cachedResult) {
          const endTime = Date.now();
          return {
            ...cachedResult,
            toolName: name,
            parameters,
            startTime,
            endTime,
            executionTime: endTime - startTime,
            context,
            metadata: {
              ...cachedResult.metadata,
              fromCache: true,
            },
          };
        }
      }

      // Execute tool with timeout
      const timeout = options.timeout || tool.timeout || this.config.defaultTimeout;
      const result = await this.executeWithTimeout(
        tool.handler(parameters, context),
        timeout
      );

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Cache result if enabled
      if (tool.cacheable && result.success && this.config.enableCaching) {
        await this.cacheResult(name, parameters, context, result, tool.cacheTtl!);
      }

      // Update stats
      await this.updateToolStats(name, true, executionTime);

      // Log execution if enabled
      if (this.config.enableAuditLogging) {
        await this.logToolExecution(name, parameters, context, result, executionTime);
      }

      const executionResult: ToolExecutionResult = {
        ...result,
        toolName: name,
        parameters,
        startTime,
        endTime,
        executionTime,
        context,
        metadata: {
          ...result.metadata,
          fromCache: false,
        },
      };

      this.logger.log(`Tool '${name}' executed successfully in ${executionTime}ms`);
      return executionResult;

    } catch (error) {
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Update stats for failure
      await this.updateToolStats(name, false, executionTime);

      this.logger.error(`Tool '${name}' execution failed: ${error.message}`);

      return {
        success: false,
        error: error.message,
        toolName: name,
        parameters,
        startTime,
        endTime,
        executionTime,
        context,
      };
    }
  }

  /**
   * Lấy thống kê performance của tool
   */
  getToolStats(name: string): ToolStats | null {
    return this.executionStats.get(name) || null;
  }

  /**
   * Lấy thống kê tất cả tools
   */
  getAllToolStats(): ToolStats[] {
    return Array.from(this.executionStats.values());
  }

  /**
   * Validate tool definition
   */
  private validateToolDefinition(tool: ToolDefinition): void {
    if (!tool.name || typeof tool.name !== 'string') {
      throw new Error('Tool name is required and must be a string');
    }

    if (!tool.description || typeof tool.description !== 'string') {
      throw new Error('Tool description is required and must be a string');
    }

    if (!tool.handler || typeof tool.handler !== 'function') {
      throw new Error('Tool handler is required and must be a function');
    }

    if (!Object.values(ToolCategory).includes(tool.category)) {
      throw new Error(`Invalid tool category: ${tool.category}`);
    }

    if (!tool.parameters || tool.parameters.type !== 'object') {
      throw new Error('Tool parameters must be an object schema');
    }
  }

  /**
   * Validate tool parameters
   */
  private validateToolParameters(tool: ToolDefinition, parameters: any): ToolValidationError[] {
    const errors: ToolValidationError[] = [];
    const schema = tool.parameters;

    // Check required parameters
    if (schema.required) {
      for (const requiredField of schema.required) {
        if (!(requiredField in parameters)) {
          errors.push({
            field: requiredField,
            message: `Required parameter '${requiredField}' is missing`,
            value: undefined,
          });
        }
      }
    }

    // Validate parameter types and constraints
    if (schema.properties) {
      for (const [fieldName, fieldSchema] of Object.entries(schema.properties)) {
        if (fieldName in parameters) {
          const value = parameters[fieldName];
          const fieldErrors = this.validateParameterField(fieldName, value, fieldSchema);
          errors.push(...fieldErrors);
        }
      }
    }

    return errors;
  }

  /**
   * Validate individual parameter field
   */
  private validateParameterField(fieldName: string, value: any, schema: any): ToolValidationError[] {
    const errors: ToolValidationError[] = [];

    // Type validation
    if (schema.type) {
      const expectedType = schema.type;
      const actualType = Array.isArray(value) ? 'array' : typeof value;
      
      if (actualType !== expectedType) {
        errors.push({
          field: fieldName,
          message: `Expected type '${expectedType}' but got '${actualType}'`,
          value,
          expected: expectedType,
        });
        return errors; // Skip further validation if type is wrong
      }
    }

    // Enum validation
    if (schema.enum && !schema.enum.includes(value)) {
      errors.push({
        field: fieldName,
        message: `Value must be one of: ${schema.enum.join(', ')}`,
        value,
        expected: schema.enum,
      });
    }

    // Number constraints
    if (typeof value === 'number') {
      if (schema.minimum !== undefined && value < schema.minimum) {
        errors.push({
          field: fieldName,
          message: `Value must be >= ${schema.minimum}`,
          value,
          expected: `>= ${schema.minimum}`,
        });
      }
      
      if (schema.maximum !== undefined && value > schema.maximum) {
        errors.push({
          field: fieldName,
          message: `Value must be <= ${schema.maximum}`,
          value,
          expected: `<= ${schema.maximum}`,
        });
      }
    }

    // String pattern validation
    if (typeof value === 'string' && schema.pattern) {
      const regex = new RegExp(schema.pattern);
      if (!regex.test(value)) {
        errors.push({
          field: fieldName,
          message: `Value does not match required pattern: ${schema.pattern}`,
          value,
          expected: schema.pattern,
        });
      }
    }

    return errors;
  }

  /**
   * Check rate limit for user
   */
  private async checkRateLimit(toolName: string, userId: number): Promise<void> {
    const tool = this.getTool(toolName);
    if (!tool || !tool.rateLimit) return;

    const key = `${toolName}:${userId}`;
    const currentMinute = Math.floor(Date.now() / 60000);
    
    if (!this.rateLimitCounters.has(toolName)) {
      this.rateLimitCounters.set(toolName, new Map());
    }
    
    const toolCounters = this.rateLimitCounters.get(toolName)!;
    const currentCount = toolCounters.get(key) || 0;
    
    if (currentCount >= tool.rateLimit) {
      throw new Error(`Rate limit exceeded for tool '${toolName}'. Limit: ${tool.rateLimit} calls per minute`);
    }
    
    toolCounters.set(key, currentCount + 1);
  }

  /**
   * Execute with timeout
   */
  private async executeWithTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(`Tool execution timeout after ${timeout}ms`)), timeout)
      ),
    ]);
  }

  /**
   * Get cached result
   */
  private async getCachedResult(
    toolName: string,
    parameters: any,
    context: ToolContext
  ): Promise<ToolResult | null> {
    try {
      const cacheKey = this.generateCacheKey(toolName, parameters, context);
      const cached = await this.redisService.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      this.logger.warn(`Failed to get cached result for tool '${toolName}': ${error.message}`);
      return null;
    }
  }

  /**
   * Cache result
   */
  private async cacheResult(
    toolName: string,
    parameters: any,
    context: ToolContext,
    result: ToolResult,
    ttl: number
  ): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(toolName, parameters, context);
      await this.redisService.setex(cacheKey, ttl, JSON.stringify(result));
    } catch (error) {
      this.logger.warn(`Failed to cache result for tool '${toolName}': ${error.message}`);
    }
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(toolName: string, parameters: any, context: ToolContext): string {
    const keyData = {
      tool: toolName,
      params: parameters,
      tenant: context.tenantId,
      user: context.userId,
    };
    return `tool_cache:${Buffer.from(JSON.stringify(keyData)).toString('base64')}`;
  }

  /**
   * Initialize tool stats
   */
  private initializeToolStats(toolName: string): void {
    if (!this.executionStats.has(toolName)) {
      this.executionStats.set(toolName, {
        toolName,
        totalCalls: 0,
        successCalls: 0,
        failedCalls: 0,
        successRate: 0,
        averageExecutionTime: 0,
        maxExecutionTime: 0,
        minExecutionTime: 0,
        cacheHits: 0,
        cacheHitRate: 0,
        lastUpdated: Date.now(),
      });
    }
  }

  /**
   * Update tool statistics
   */
  private async updateToolStats(toolName: string, success: boolean, executionTime: number): Promise<void> {
    const stats = this.executionStats.get(toolName);
    if (!stats) return;

    stats.totalCalls++;
    if (success) {
      stats.successCalls++;
    } else {
      stats.failedCalls++;
    }

    stats.successRate = stats.successCalls / stats.totalCalls;
    stats.averageExecutionTime = 
      (stats.averageExecutionTime * (stats.totalCalls - 1) + executionTime) / stats.totalCalls;
    stats.maxExecutionTime = Math.max(stats.maxExecutionTime, executionTime);
    stats.minExecutionTime = stats.minExecutionTime === 0 
      ? executionTime 
      : Math.min(stats.minExecutionTime, executionTime);
    stats.lastUpdated = Date.now();

    // Persist stats to Redis periodically
    if (stats.totalCalls % 10 === 0) {
      await this.persistToolStats(toolName, stats);
    }
  }

  /**
   * Load tool stats from Redis
   */
  private async loadToolStats(): Promise<void> {
    try {
      const keys = await this.redisService.keys('tool_stats:*');
      for (const key of keys) {
        const statsData = await this.redisService.get(key);
        if (statsData) {
          const stats: ToolStats = JSON.parse(statsData);
          this.executionStats.set(stats.toolName, stats);
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to load tool stats: ${error.message}`);
    }
  }

  /**
   * Persist tool stats to Redis
   */
  private async persistToolStats(toolName: string, stats: ToolStats): Promise<void> {
    try {
      const key = `tool_stats:${toolName}`;
      await this.redisService.setex(key, 86400, JSON.stringify(stats)); // 24 hours TTL
    } catch (error) {
      this.logger.warn(`Failed to persist tool stats for '${toolName}': ${error.message}`);
    }
  }

  /**
   * Log tool execution
   */
  private async logToolExecution(
    toolName: string,
    parameters: any,
    context: ToolContext,
    result: ToolResult,
    executionTime: number
  ): Promise<void> {
    try {
      const logEntry = {
        toolName,
        parameters,
        context: {
          tenantId: context.tenantId,
          userId: context.userId,
          conversationId: context.conversationId,
        },
        result: {
          success: result.success,
          error: result.error,
        },
        executionTime,
        timestamp: Date.now(),
      };

      const key = `tool_log:${Date.now()}:${toolName}:${context.userId}`;
      await this.redisService.setex(key, 604800, JSON.stringify(logEntry)); // 7 days TTL
    } catch (error) {
      this.logger.warn(`Failed to log tool execution: ${error.message}`);
    }
  }

  /**
   * Start rate limit cleanup job
   */
  private startRateLimitCleanup(): void {
    setInterval(() => {
      const currentMinute = Math.floor(Date.now() / 60000);
      
      for (const [toolName, counters] of this.rateLimitCounters.entries()) {
        for (const [key, _] of counters.entries()) {
          // Remove counters older than 1 minute
          const keyMinute = Math.floor(Date.now() / 60000);
          if (keyMinute < currentMinute) {
            counters.delete(key);
          }
        }
      }
    }, 60000); // Run every minute
  }
}
